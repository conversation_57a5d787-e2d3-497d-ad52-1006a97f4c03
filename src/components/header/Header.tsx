'use client';
import avatarIcon from '@/assets/images/avatar-icon.svg';
import hamburgerIcon from '@/assets/images/dashboard/hamburger-icon.svg';
import logo from '@/assets/images/logos/unity-logo.svg';
import { AppRoutePaths } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { ChevronRightIcon, UserIcon, X } from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../ui/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/DropDownMenu';
import { Sheet, SheetContent, SheetFooter, SheetTitle, SheetTrigger } from '../ui/Sheet';
import <PERSON><PERSON>ogo from '../unity-logo/UnityLogo';
import { menuList } from './data';
import Notifications from '../notifications/Notifications';
import { useIsMobile } from '@/hooks/use-mobile';

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { signOut, userData } = useAuthContext();
  const isMobile = useIsMobile();
  console.log('isMobile: ', isMobile);

  const [isOpenMobileMenu, setIsOpenMobileMenu] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);

  // Check if the current route is active for styling
  const isActive = (path: string) => location.pathname.startsWith(path);

  // Handle guarded navigation via context
  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <div className='flex bg-white items-center justify-between w-full sm:px-8 px-4 h-[72px]'>
      {/* Logo and desktop navigation */}
      <div className='flex items-center gap-4'>
        <UnityLogo />
        <div className='items-center gap-4 hidden sm:flex'>
          {menuList &&
            menuList.map((item) => {
              return (
                <button
                  key={item.name}
                  onClick={() => navigate(item.url)}
                  className={cn(
                    'px-3 py-2 rounded-md text-gray-700 text-base font-semibold',
                    isActive(item.url) && 'text-primary bg-secondary'
                  )}
                >
                  {item.name}
                </button>
              );
            })}
        </div>
      </div>

      {/* User dropdown (desktop) */}
      <div className='items-center gap-4 hidden sm:flex'>
        <Notifications />

        <DropdownMenu open={isProfileDropdownOpen} onOpenChange={setIsProfileDropdownOpen}>
          <DropdownMenuTrigger>
            <UserIcon
              className={cn(
                'w-10 h-10 bg-secondary text-primary p-2 rounded-full hover:ring-2 hover:ring-[#E8EBFD]',
                isProfileDropdownOpen && 'ring-2 ring-[#E8EBFD]'
              )}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' sideOffset={10}>
            <DropdownMenuItem className='px-4 py-3 max-w-60 focus:bg-white'>
              <div className='flex items-center gap-4'>
                <img src={avatarIcon} alt='profile' className='w-10 h-10 shrink-0 ' />
                <div className='flex flex-col gap-1 flex-1 w-full max-w-40'>
                  <p className='text-gray-700 text-sm font-semibold truncate'>
                    {userData?.firstName} {userData?.lastName}
                  </p>
                  <p className='text-gray-600 text-sm font-normal truncate'>{userData?.email}</p>
                </div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setIsProfileDropdownOpen(false);
                navigate(AppRoutePaths.SETTINGS);
              }}
              className='font-medium text-gray-700 px-4 py-2'
            >
              View Profile
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={signOut} className='font-medium text-gray-700 px-4 py-2'>
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Mobile menu */}
      <div className='items-center gap-4 sm:hidden flex'>
        {isMobile && <Notifications />}
        <Sheet open={isOpenMobileMenu} onOpenChange={setIsOpenMobileMenu}>
          <SheetTrigger>
            <img src={hamburgerIcon} alt='hamburger' className='w-6 h-6' />
          </SheetTrigger>
          <SheetContent hideClose className='w-full flex flex-col p-0'>
            <div className='p-4 w-full'>
              <SheetTitle className='flex items-center justify-between'>
                <img src={logo} alt='logo' />
                <X className='w-6 h-6 text-gray-500' onClick={() => setIsOpenMobileMenu(false)} />
              </SheetTitle>
              <div className='flex flex-col gap-2 py-6'>
                {menuList &&
                  menuList.map((item) => (
                    <Link
                      key={item.name}
                      to={item.url}
                      onClick={(e) => {
                        e.preventDefault();
                        setIsOpenMobileMenu(false);
                        handleNavigate(item.url);
                      }}
                      className={cn(
                        'flex py-2 items-center justify-between rounded-md transition-colors',
                        'hover:bg-secondary active:bg-secondary'
                      )}
                    >
                      <span className='text-base text-gray-900 font-semibold'>{item.name}</span>
                      <ChevronRightIcon className='w-5 h-5 text-gray-500' />
                    </Link>
                  ))}
              </div>
            </div>
            <div className='mt-auto border-t border-gray-100'>
              <div className='flex items-center gap-3 px-4 py-6'>
                <UserIcon className='w-10 h-10 bg-secondary text-primary p-2 rounded-full' />
                <div className='flex flex-col'>
                  <p className='text-[#191E3B] text-base font-semibold'>
                    {userData?.firstName} {userData?.lastName}
                  </p>
                  <p className='text-[#52577A] text-base'>{userData?.email}</p>
                </div>
              </div>
              <SheetFooter className='px-4 pb-4'>
                <Button
                  variant='outline'
                  className='w-full text-base font-semibold text-gray-700'
                  onClick={signOut}
                >
                  Logout
                </Button>
              </SheetFooter>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
};

export default Header;
