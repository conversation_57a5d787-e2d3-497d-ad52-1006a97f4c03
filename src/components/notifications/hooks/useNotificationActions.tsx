import {
  useAdminDeleteClubEventMutation,
  useAdminRemoveClubPostByIdMutation,
  useAdminUnflagReportsByEventIdMutation,
  useAdminUnflagReportsByPostIdMutation,
  useMarkAdminNotificationsAsReadMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

const useNotificationActions = ({ refetch }: { refetch?: () => void }) => {
  const { toast } = useToast();
  const navigate = useNavigate();

  const [unflagPostMutation, { loading: isUnflaggingPost }] =
    useAdminUnflagReportsByPostIdMutation();
  const [unflagEventMutation, { loading: isUnflaggingEvent }] =
    useAdminUnflagReportsByEventIdMutation();
  const [removePostMutation, { loading: isRemovingPost }] = useAdminRemoveClubPostByIdMutation();
  const [removeEventMutation, { loading: isRemovingEvent }] = useAdminDeleteClubEventMutation();
  const [markAdminNotificationsAsRead, { loading: isMarkingAsRead }] =
    useMarkAdminNotificationsAsReadMutation();

  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleUnflagNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string) => {
      try {
        if (isPostNotification) {
          await unflagPostMutation({
            variables: {
              postId: notificationId,
            },
            update: (cache) => {
              // Optimistically remove the notification from cache
              cache.modify({
                fields: {
                  adminNotifications(existingData, { readField }) {
                    if (!existingData?.items) return existingData;

                    return {
                      ...existingData,
                      items: existingData.items.filter((item: any) => {
                        const payload = readField('payload', item) as any;
                        return payload?.postId !== notificationId;
                      }),
                      total: Math.max(0, (existingData.total || 0) - 1),
                    };
                  },
                },
              });
            },
          });
        } else {
          await unflagEventMutation({
            variables: {
              eventId: notificationId,
            },
            update: (cache) => {
              // Optimistically remove the notification from cache
              cache.modify({
                fields: {
                  adminNotifications(existingData, { readField }) {
                    if (!existingData?.items) return existingData;

                    return {
                      ...existingData,
                      items: existingData.items.filter((item: any) => {
                        const payload = readField('payload', item) as any;
                        return payload?.eventId !== notificationId;
                      }),
                      total: Math.max(0, (existingData.total || 0) - 1),
                    };
                  },
                },
              });
            },
          });
        }
        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} unflagged successfully`,
          duration: TOAST_DURATION,
        });

        // Refetch to ensure data consistency
        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to unflag notification');
        // Refetch on error to restore correct state
        refetch?.();
      }
    },
    [refetch, unflagPostMutation, unflagEventMutation, handleApiError, toast]
  );

  const handleRemoveNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string) => {
      try {
        if (isPostNotification) {
          await removePostMutation({
            variables: {
              postId: notificationId,
            },
            update: (cache) => {
              // Optimistically remove the notification from cache
              cache.modify({
                fields: {
                  adminNotifications(existingData, { readField }) {
                    if (!existingData?.items) return existingData;

                    return {
                      ...existingData,
                      items: existingData.items.filter((item: any) => {
                        const payload = readField('payload', item) as any;
                        return payload?.postId !== notificationId;
                      }),
                      total: Math.max(0, (existingData.total || 0) - 1),
                    };
                  },
                },
              });
            },
          });
        } else {
          await removeEventMutation({
            variables: {
              clubEventId: notificationId,
            },
            update: (cache) => {
              // Optimistically remove the notification from cache
              cache.modify({
                fields: {
                  adminNotifications(existingData, { readField }) {
                    if (!existingData?.items) return existingData;

                    return {
                      ...existingData,
                      items: existingData.items.filter((item: any) => {
                        const payload = readField('payload', item) as any;
                        return payload?.eventId !== notificationId;
                      }),
                      total: Math.max(0, (existingData.total || 0) - 1),
                    };
                  },
                },
              });
            },
          });
        }

        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} removed successfully`,
          duration: TOAST_DURATION,
        });

        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to remove notification');
        // Refetch on error to restore correct state
        refetch?.();
      }
    },
    [refetch, removePostMutation, removeEventMutation, handleApiError, toast]
  );

  const handleMarkAsRead = useCallback(
    async ({
      isMarkAll = false,
      notificationId,
    }: {
      isMarkAll?: boolean;
      notificationId?: string;
    }) => {
      try {
        await markAdminNotificationsAsRead({
          variables: {
            input: {
              all: isMarkAll,
              notificationIds: notificationId ? [notificationId] : undefined,
            },
          },
        });
        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to mark admin notifications as read');
      }
    },
    [markAdminNotificationsAsRead, refetch, handleApiError]
  );

  const handleNavigate = useCallback(
    ({
      isPostReport,
      associationId,
      clubId,
    }: {
      isPostReport: boolean;
      associationId?: string;
      clubId?: string;
    }) => {
      const navigatePath = `/associations/${associationId}/clubs/${clubId}`;
      if (isPostReport) {
        navigate(`${navigatePath}?tab=posts`);
      } else {
        navigate(`${navigatePath}?tab=events`);
      }
    },
    [navigate]
  );

  return {
    handleUnflagNotification,
    handleRemoveNotification,
    handleNavigate,
    isUnflagging: isUnflaggingPost || isUnflaggingEvent,
    isRemoving: isRemovingPost || isRemovingEvent,
    handleMarkAsRead,
    isMarkingAsRead,
  };
};

export default useNotificationActions;
