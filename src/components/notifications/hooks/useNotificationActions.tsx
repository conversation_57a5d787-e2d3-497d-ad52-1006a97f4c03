import {
  AdminNotificationsDocument,
  useAdminDeleteClubEventMutation,
  useAdminRemoveClubPostByIdMutation,
  useAdminUnflagReportsByEventIdMutation,
  useAdminUnflagReportsByPostIdMutation,
  useMarkAdminNotificationsAsReadMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError, useApolloClient } from '@apollo/client';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

const useNotificationActions = ({ refetch }: { refetch?: () => void }) => {
  const { toast } = useToast();
  const client = useApolloClient();
  const navigate = useNavigate();

  const [unflagPostMutation, { loading: isUnflaggingPost }] =
    useAdminUnflagReportsByPostIdMutation();
  const [unflagEventMutation, { loading: isUnflaggingEvent }] =
    useAdminUnflagReportsByEventIdMutation();
  const [removePostMutation, { loading: isRemovingPost }] = useAdminRemoveClubPostByIdMutation();
  const [removeEventMutation, { loading: isRemovingEvent }] = useAdminDeleteClubEventMutation();
  const [markAdminNotificationsAsRead, { loading: isMarkingAsRead }] =
    useMarkAdminNotificationsAsReadMutation();

  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleUnflagNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string) => {
      try {
        if (isPostNotification) {
          await unflagPostMutation({
            variables: {
              postId: notificationId,
            },
          });
        } else {
          await unflagEventMutation({
            variables: {
              eventId: notificationId,
            },
          });
        }
        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} unflagged successfully`,
          duration: TOAST_DURATION,
        });

        // Use direct refetch to ensure current page data is updated
        refetch?.();

        // Also refetch all admin notification queries to ensure consistency
        client.refetchQueries({
          include: [AdminNotificationsDocument],
        });
      } catch (error) {
        handleApiError(error, 'Failed to unflag notification');
      }
    },
    [refetch, unflagPostMutation, unflagEventMutation, handleApiError]
  );

  const handleRemoveNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string) => {
      try {
        if (isPostNotification) {
          await removePostMutation({
            variables: {
              postId: notificationId,
            },
          });
        } else {
          await removeEventMutation({
            variables: {
              clubEventId: notificationId,
            },
          });
        }

        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} removed successfully`,
          duration: TOAST_DURATION,
        });

        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to remove notification');
      }
    },
    [refetch, removePostMutation, removeEventMutation, handleApiError]
  );

  const handleMarkAsRead = useCallback(
    async ({
      isMarkAll = false,
      notificationId,
    }: {
      isMarkAll?: boolean;
      notificationId?: string;
    }) => {
      try {
        await markAdminNotificationsAsRead({
          variables: {
            input: {
              all: isMarkAll,
              notificationIds: notificationId ? [notificationId] : undefined,
            },
          },
        });
        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to mark admin notifications as read');
      }
    },
    [markAdminNotificationsAsRead, refetch, handleApiError]
  );

  const handleNavigate = useCallback(
    ({
      isPostReport,
      associationId,
      clubId,
    }: {
      isPostReport: boolean;
      associationId?: string;
      clubId?: string;
    }) => {
      const navigatePath = `/associations/${associationId}/clubs/${clubId}`;
      if (isPostReport) {
        navigate(`${navigatePath}?tab=posts`);
      } else {
        navigate(`${navigatePath}?tab=events`);
      }
    },
    [navigate]
  );

  return {
    handleUnflagNotification,
    handleRemoveNotification,
    handleNavigate,
    isUnflagging: isUnflaggingPost || isUnflaggingEvent,
    isRemoving: isRemovingPost || isRemovingEvent,
    handleMarkAsRead,
    isMarkingAsRead,
  };
};

export default useNotificationActions;
