import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { cn, formatNotificationTime } from '@/lib/utils';
import { Flag } from 'lucide-react';
import { Skeleton } from '@/components/ui/Skeleton';
import {
  AdminNotification,
  AdminNotificationType,
  AdminReportActionType,
  ReportStatusEnum,
} from '@/generated/graphql';
import { memo, useCallback, useMemo } from 'react';
import useNotificationActions from './hooks/useNotificationActions';

interface NotificationItemProps {
  item: AdminNotification;
  actions: ReturnType<typeof useNotificationActions>;
  isAdmin?: boolean;
  handleCloseNotification: () => void;
}

const NotificationItem = ({
  item,
  actions,
  isAdmin,
  handleCloseNotification,
}: NotificationItemProps) => {
  const {
    handleUnflagNotification,
    handleRemoveNotification,
    handleMarkAsRead,
    handleNavigate,
    isUnflagging,
    isRemoving,
  } = actions;
  const isRead = item.isRead;
  console.log('render');

  const isPostReport = item.type === AdminNotificationType.ClubPostReport;
  const hasOpenReports = isPostReport
    ? item.clubPost?.reports?.some((report) => report.status === ReportStatusEnum.Open)
    : item.clubEvent?.reports?.some((report) => report.status === ReportStatusEnum.Open);

  const clubProfile = item.clubPost?.clubProfile;
  const displayName = isPostReport
    ? (clubProfile?.user?.firstName ?? '') + ' ' + (clubProfile?.user?.lastName ?? '')
    : (item.clubEvent?.clubProfile?.user?.firstName ?? '') +
      ' ' +
      (item.clubEvent?.clubProfile?.user?.lastName ?? '');
  const avatarUrl = isPostReport ? (clubProfile?.img?.url ?? undefined) : undefined;

  const initials =
    (displayName || '')
      .split(' ')
      .map((s) => s[0])
      .filter(Boolean)
      .slice(0, 2)
      .join('')
      .toUpperCase() || 'UN';

  const categoryTitle = (() => {
    const reports = isPostReport
      ? item.clubPost?.reports?.filter((rp) => rp.status === ReportStatusEnum.Open)
      : item.clubEvent?.reports?.filter((rp) => rp.status === ReportStatusEnum.Open);

    if (!reports || reports.length === 0) return 'Other';

    // Get the latest report (most recent createdAt)
    const latestReport = reports.reduce((latest, current) => {
      return new Date(current.createdAt) > new Date(latest.createdAt) ? current : latest;
    });

    return latestReport.category?.title ?? 'Other';
  })();

  const description = isPostReport
    ? (item.clubPost?.content ?? '')
    : (item.clubEvent?.description ?? '');

  const unflagLabel = isPostReport ? 'Unflag Post' : 'Unflag Event';
  const deleteLabel = isPostReport ? 'Delete Post' : 'Delete Event';
  const hasUnflagAction = useMemo(
    () => item.action === AdminReportActionType.Unflag,
    [item.action]
  );
  const hasDeleteAction = useMemo(
    () => item.action === AdminReportActionType.Delete,
    [item.action]
  );

  const handleUnflagItem = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (isPostReport) {
        handleUnflagNotification(isPostReport, item.clubPost?.id ?? '');
      } else {
        handleUnflagNotification(isPostReport, item.clubEvent?.id ?? '');
      }
    },
    [isPostReport, item.clubPost?.id, item.clubEvent?.id, handleUnflagNotification]
  );

  const handleRemoveItem = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (isPostReport) {
        handleRemoveNotification(isPostReport, item.clubPost?.id ?? '');
      } else {
        handleRemoveNotification(isPostReport, item.clubEvent?.id ?? '');
      }
    },
    [isPostReport, handleRemoveNotification, item.clubPost?.id, item.clubEvent?.id]
  );

  const handleItemClick = useCallback(() => {
    if (!isRead && isAdmin) {
      handleMarkAsRead({ notificationId: item.id });
    }
    const associationId = isPostReport
      ? (item.clubPost?.clubProfile?.user?.association?.id ?? '')
      : (item.clubEvent?.clubProfile?.user?.association?.id ?? '');
    const clubId = isPostReport ? (item.clubPost?.clubId ?? '') : (item.clubEvent?.clubId ?? '');
    if (associationId && clubId) {
      handleNavigate({
        isPostReport,
        associationId,
        clubId,
      });
    }
    handleCloseNotification();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRead, handleNavigate, isPostReport, item.id, handleMarkAsRead]);

  return (
    <div
      onClick={handleItemClick}
      className={cn(
        'w-full p-4 gap-3 flex flex-col bg-muted sm:p-6 sm:gap-4',
        !isRead && 'bg-[#FCEFED80] border-b-[1px]'
      )}
    >
      <div className='flex justify-between items-start'>
        <div className='flex flex-col gap-1 sm:flex-row sm:gap-4 sm:items-center'>
          <p className='text-sm font-medium text-gray-900'>
            {isPostReport ? 'Flagged Post' : 'Flagged Event'}
          </p>
          <p className='text-[13px] leading-5 text-gray-700'>
            {formatNotificationTime(item.reportedAt)}
          </p>
        </div>
        {!isRead && <div className='bg-destructive w-2 h-2 rounded-full flex-shrink-0 mt-1' />}
      </div>

      <div className='flex items-center justify-between gap-2 min-w-0 flex-1'>
        <div className='flex items-center gap-2 sm:gap-3 min-w-0'>
          <Avatar className='w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0'>
            <AvatarImage src={avatarUrl} />
            <AvatarFallback className='bg-gradient-to-br from-purple-400 to-yellow-400 text-white text-xs sm:text-sm font-medium'>
              {initials}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm text-gray-900 font-medium truncate min-w-0'>{displayName}</span>
        </div>
        {hasOpenReports && (
          <Badge className='bg-[#FCEFED] px-1.5 py-1 hover:bg-[#FCEFED] gap-1 font-medium text-xs text-destructive-foreground border-none whitespace-nowrap flex-shrink-0 sm:px-2 sm:text-sm'>
            <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
            <span className='truncate max-w-[80px] sm:max-w-none'>{categoryTitle}</span>
          </Badge>
        )}
      </div>
      {description ? <p className='text-sm text-gray-700'>{description}</p> : null}
      <div className='w-full flex justify-between gap-2 sm:gap-3'>
        <Button
          variant={'outline'}
          className={cn(
            'flex-1 z-10 text-gray-700 text-xs sm:text-sm h-8 sm:h-10',
            hasUnflagAction && 'cursor-not-allowed'
          )}
          onClick={handleUnflagItem}
          disabled={hasUnflagAction || isUnflagging || !isAdmin}
        >
          {unflagLabel}
        </Button>
        <Button
          variant={'destructive'}
          className={cn(
            'flex-1 text-white text-xs sm:text-sm h-8 sm:h-10',
            hasDeleteAction && 'cursor-not-allowed'
          )}
          onClick={handleRemoveItem}
          disabled={hasDeleteAction || isRemoving || !isAdmin}
        >
          {deleteLabel}
        </Button>
      </div>
    </div>
  );
};

export default memo(NotificationItem);

export const PostNotificationItemSkeleton = () => {
  return (
    <div className='w-full p-4 gap-3 flex flex-col sm:p-6 sm:gap-4'>
      <div className='flex justify-between items-start'>
        <div className='flex flex-col gap-1 sm:flex-row sm:gap-4'>
          <Skeleton className='h-4 w-28' />
          <Skeleton className='h-4 w-16' />
        </div>
        <Skeleton className='h-2 w-2 rounded-full' />
      </div>

      <div className='flex items-center justify-between gap-2 min-w-0 flex-1'>
        <div className='flex items-center gap-2 sm:gap-3'>
          <Skeleton className='w-8 h-8 sm:w-10 sm:h-10 rounded-full' />
          <Skeleton className='h-4 w-32' />
        </div>
        <Skeleton className='h-6 w-20 rounded-full' />
      </div>

      <Skeleton className='h-4 w-full' />
      <Skeleton className='h-4 w-4/5' />

      <div className='w-full flex justify-between gap-2 sm:gap-3'>
        <Skeleton className='h-8 w-full sm:h-10' />
        <Skeleton className='h-8 w-full sm:h-10' />
      </div>
    </div>
  );
};
