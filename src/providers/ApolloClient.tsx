import { Apollo<PERSON><PERSON><PERSON> } from '@apollo/client';
import {
  ApolloClient,
  ApolloLink,
  HttpLink,
  InMemoryCache,
  NormalizedCacheObject,
  fromPromise,
} from '@apollo/client/core';
import { setContext } from '@apollo/client/link/context';
import { ErrorResponse, onError } from '@apollo/client/link/error';
import React, { useMemo } from 'react';
import removeEmptyKeys from '@/lib/RemoveEmptyKeys';
import { refreshTokenVar } from '@/lib/cache';
interface Props {
  children: React.ReactNode;
}

type ReturnType = {
  client: ApolloClient<NormalizedCacheObject>;
};

const ApolloClientContext = React.createContext<ReturnType | undefined>(undefined);

export const useApolloClient = () => React.useContext(ApolloClientContext);

const getHeaders: any = async (headers: any) => {
  const authData = localStorage.getItem('auth');
  const { accessToken } = authData ? JSON.parse(authData) : { accessToken: undefined };

  return removeEmptyKeys({
    ...headers,
    Authorization: accessToken ? `Bearer ${accessToken}` : '',
  });
};

const ApolloClientProvider = ({ children }: Props) => {
  const client = useMemo(() => {
    const authLink = setContext(async (_, { headers }) => {
      const newHeaders = await getHeaders(headers);
      return {
        headers: newHeaders,
      };
    });

    const cache = new InMemoryCache({
      typePolicies: {
        Query: {
          fields: {
            associations: {
              keyArgs: ['search'],
              merge(existing, incoming) {
                if (!existing) return incoming;

                const existingItems = existing.items || [];
                const incomingItems = incoming.items || [];

                return {
                  ...incoming,
                  items: [...existingItems, ...incomingItems],
                };
              },
            },
            adminNotifications: {
              keyArgs: false, // Don't use keyArgs to allow proper pagination merging
              merge(existing, incoming, { args, variables }) {
                if (!existing) return incoming;

                const incomingPage =
                  args?.paginationArgs?.page || variables?.paginationArgs?.page || 1;
                const incomingLimit =
                  args?.paginationArgs?.limit || variables?.paginationArgs?.limit || 10;

                // If this is page 1, always replace the data completely
                if (incomingPage === 1) {
                  return incoming;
                }

                const existingItems = existing.items || [];
                const incomingItems = incoming.items || [];

                // Calculate how many items we should have for the incoming page
                const expectedItemsForPage = (incomingPage - 1) * incomingLimit;

                // If we have fewer existing items than expected, we're loading more pages
                if (existingItems.length < expectedItemsForPage) {
                  return {
                    ...incoming,
                    items: [...existingItems, ...incomingItems],
                  };
                }

                // If we have the expected number or more, this is a refetch of existing data
                // Replace only the items for this specific page
                const updatedItems = [...existingItems];
                const startIndex = expectedItemsForPage;

                // Replace the items for this page
                updatedItems.splice(startIndex, incomingItems.length, ...incomingItems);

                return {
                  ...incoming,
                  items: updatedItems,
                };
              },
            },
          },
        },
      },
    });

    const errorLink: any = onError(
      ({ graphQLErrors, networkError, response: _response, operation, forward }: ErrorResponse) => {
        if (
          (networkError as any)?.statusCode === 401 ||
          (graphQLErrors?.[0]?.extensions?.code === 'UNAUTHORIZED' &&
            graphQLErrors?.[0]?.message === 'Jwt expired')
        ) {
          // Don't try to refresh if the operation was a refresh token request
          if (operation.operationName === 'RefreshToken') {
            return;
          }

          // If the error is a 401, we need to refresh the token and try again.
          const headers = operation.getContext().headers;
          return fromPromise(
            refreshTokenVar()!().then((result) => {
              if (!result) {
                // If refresh failed, don't retry the operation
                return false;
              }
              return getHeaders(headers).then((newHeaders: any) => {
                operation.setContext({
                  headers: newHeaders,
                });
                return true;
              });
            })
          )
            .filter((value) => !!value)
            .flatMap(() => forward(operation));
        }
      }
    );

    const client = new ApolloClient({
      defaultOptions: {
        watchQuery: {
          errorPolicy: 'all',
        },
      },
      link: ApolloLink.from([
        authLink,
        errorLink,
        new HttpLink({
          uri: (() => {
            return process.env.REACT_APP_API_URL;
          })(),
        }),
      ]),
      cache,
    });

    return {
      client,
    };
  }, []);

  return (
    <ApolloClientContext.Provider value={client}>
      <ApolloProvider client={client?.client}>{children}</ApolloProvider>
    </ApolloClientContext.Provider>
  );
};

export { ApolloClientContext, ApolloClientProvider };
